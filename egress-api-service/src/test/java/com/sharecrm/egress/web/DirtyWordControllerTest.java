package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.DirtyWordsRequest;
import com.sharecrm.egress.entity.HasDirtyWordsResponse;
import com.sharecrm.egress.entity.ParseDirtyWordsItem;
import com.sharecrm.egress.entity.ParseDirtyWordsResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.DirtyWordService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.reactive.server.WebTestClient;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@WebFluxTest(controllers = DirtyWordController.class)
class DirtyWordControllerTest {

  @Autowired
  private WebTestClient webTestClient;
  @MockitoBean
  private DirtyWordService dirtyWordService;

  @Test
  void parseFunction() {
    //setup
    List<ParseDirtyWordsItem> data = List.of(new ParseDirtyWordsItem("test", "index"));
    // Mock
    when(dirtyWordService.parseForFunction(any(), any())).thenReturn(data);
    // Test
    webTestClient
      .post()
      .uri("/api/v2/dirty-words/parse/{group}", "sms")
      .bodyValue("this is text")
      .exchange()
      .expectStatus()
      .isOk()
      .expectBodyList(new ParameterizedTypeReference<ParseDirtyWordsItem>() {
      })
      .hasSize(data.size());
  }

  @Test
  void hasDirtyWords() {
    DirtyWordsRequest request = new DirtyWordsRequest();
    request.setGroup("test");
    request.setText("this is text");
    ResponseEntity<EgressApiResponse<HasDirtyWordsResponse>> response = new ResponseEntity<>(HttpStatus.OK);
    when(dirtyWordService.hasDirtyWords(any())).thenReturn(response);

    webTestClient
      .post()
      .uri("/api/v2/dirty-words/has")
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk();
  }
  
  @Test
  void parse(){
    DirtyWordsRequest request = new DirtyWordsRequest();
    request.setGroup("test");
    request.setText("this is text");
    ResponseEntity<EgressApiResponse<ParseDirtyWordsResponse>> response = new ResponseEntity<>(HttpStatus.OK);
    when(dirtyWordService.parse(any(DirtyWordsRequest.class))).thenReturn(response);
    webTestClient
      .post()
      .uri("/api/v2/dirty-words/parse")
      .bodyValue(request)
      .exchange()
      .expectStatus()
      .isOk();
  }

  @Test
  void parseFunctionWithEmptyText() {
    // Given
    when(dirtyWordService.parseForFunction(any(), any())).thenReturn(List.of());

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/dirty-words/parse/{group}", "sms")
        .bodyValue("")
        .exchange()
        .expectStatus()
        .isOk()
        .expectBodyList(new ParameterizedTypeReference<ParseDirtyWordsItem>() {
        })
        .hasSize(0);
  }

  @Test
  void parseFunctionWithDifferentGroups() {
    // Given
    List<ParseDirtyWordsItem> data = List.of(new ParseDirtyWordsItem("bad", "0-3"));
    when(dirtyWordService.parseForFunction(any(), any())).thenReturn(data);

    // Test different groups
    String[] groups = { "sms", "email", "push", "general" };
    for (String group : groups) {
      webTestClient
          .post()
          .uri("/api/v2/dirty-words/parse/{group}", group)
          .bodyValue("bad word test")
          .exchange()
          .expectStatus()
          .isOk()
          .expectBodyList(new ParameterizedTypeReference<ParseDirtyWordsItem>() {
          })
          .hasSize(1);
    }
  }

  @Test
  void hasDirtyWordsWithPositiveResult() {
    // Given
    DirtyWordsRequest request = new DirtyWordsRequest();
    request.setGroup("sms");
    request.setText("this contains bad words");

    EgressApiResponse<HasDirtyWordsResponse> apiResponse = new EgressApiResponse<>();
    HasDirtyWordsResponse hasResponse = new HasDirtyWordsResponse();
    hasResponse.setHasDirtyWords(true);
    apiResponse.setData(hasResponse);
    apiResponse.setCode(200);
    apiResponse.setMessage("ok");

    ResponseEntity<EgressApiResponse<HasDirtyWordsResponse>> response = new ResponseEntity<>(apiResponse,
        HttpStatus.OK);
    when(dirtyWordService.hasDirtyWords(any())).thenReturn(response);

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/dirty-words/has")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data.hasDirtyWords").isEqualTo(true);
  }

  @Test
  void hasDirtyWordsWithNegativeResult() {
    // Given
    DirtyWordsRequest request = new DirtyWordsRequest();
    request.setGroup("sms");
    request.setText("this is clean text");

    EgressApiResponse<HasDirtyWordsResponse> apiResponse = new EgressApiResponse<>();
    HasDirtyWordsResponse hasResponse = new HasDirtyWordsResponse();
    hasResponse.setHasDirtyWords(false);
    apiResponse.setData(hasResponse);
    apiResponse.setCode(200);
    apiResponse.setMessage("ok");

    ResponseEntity<EgressApiResponse<HasDirtyWordsResponse>> response = new ResponseEntity<>(apiResponse,
        HttpStatus.OK);
    when(dirtyWordService.hasDirtyWords(any())).thenReturn(response);

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/dirty-words/has")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data.hasDirtyWords").isEqualTo(false);
  }

  @Test
  void parseWithDetailedResponse() {
    // Given
    DirtyWordsRequest request = new DirtyWordsRequest();
    request.setGroup("sms");
    request.setText("this contains bad words");

    EgressApiResponse<ParseDirtyWordsResponse> apiResponse = new EgressApiResponse<>();
    ParseDirtyWordsResponse parseResponse = new ParseDirtyWordsResponse();
    parseResponse.setHitWords(List.of()); // Empty list for simplicity
    apiResponse.setData(parseResponse);
    apiResponse.setCode(200);
    apiResponse.setMessage("ok");

    ResponseEntity<EgressApiResponse<ParseDirtyWordsResponse>> response = new ResponseEntity<>(apiResponse,
        HttpStatus.OK);
    when(dirtyWordService.parse(any(DirtyWordsRequest.class))).thenReturn(response);

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/dirty-words/parse")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.data.items").isArray()
        .jsonPath("$.data.cleanText").isEqualTo("this contains *** words");
  }

  @Test
  void shouldHandleInvalidRequest() {
    // Given - request with null/empty fields
    DirtyWordsRequest request = new DirtyWordsRequest();
    // group and text are null

    // When & Then
    webTestClient
        .post()
        .uri("/api/v2/dirty-words/has")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isBadRequest();
  }
}