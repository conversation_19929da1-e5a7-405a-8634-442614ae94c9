package com.sharecrm.egress.web;

import com.sharecrm.egress.config.TestBeanConfig;
import com.sharecrm.egress.entity.XunFeiSignatureRequest;
import com.sharecrm.egress.entity.XunFeiSignatureResponse;
import com.sharecrm.egress.service.XunFeiService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * XunFeiController单元测试
 */
@WebFluxTest(value = XunFeiController.class)
@Import({ TestBeanConfig.class })
class XunFeiControllerTest {

  @Autowired
  private WebTestClient webTestClient;

  @MockitoBean
  private XunFeiService xunFeiService;

  @Test
  void shouldReturnSuccessWhenSignatureRequestIsValid() {
    // Given
    XunFeiSignatureRequest request = new XunFeiSignatureRequest();
    request.setTenantId("12345");
    request.setProtocol("wss");
    request.setMethod("GET");
    request.setHost("iat.xf-yun.com");
    request.setPath("/v1");

    XunFeiSignatureResponse response = new XunFeiSignatureResponse();
    response.setAppId("test-app-id");
    response.setResId("test-res-id");
    response.setUrl("wss://iat.xf-yun.com/v1?authorization=test&date=test&host=iat.xf-yun.com");

    when(xunFeiService.sign(any())).thenReturn(Mono.just(response));

    // When & Then
    webTestClient.post()
        .uri("/xf/signature")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.message").isEqualTo("ok")
        .jsonPath("$.data.appId").isEqualTo("test-app-id")
        .jsonPath("$.data.resId").isEqualTo("test-res-id")
        .jsonPath("$.data.url").isEqualTo("wss://iat.xf-yun.com/v1?authorization=test&date=test&host=iat.xf-yun.com");
  }

  @Test
  void shouldReturnBadRequestWhenServiceThrowsException() {
    // Given
    XunFeiSignatureRequest request = new XunFeiSignatureRequest();
    request.setTenantId("12345");

    when(xunFeiService.sign(any())).thenReturn(Mono.error(new RuntimeException("Service error")));

    // When & Then
    webTestClient.post()
        .uri("/xf/signature")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(400)
        .jsonPath("$.message").isEqualTo("Bad Request");
  }

  @Test
  void shouldHandleEmptyRequest() {
    // Given
    XunFeiSignatureRequest request = new XunFeiSignatureRequest();

    XunFeiSignatureResponse response = new XunFeiSignatureResponse();
    response.setAppId("default-app-id");
    response.setResId("default-res-id");
    response.setUrl("wss://iat.xf-yun.com/v1?authorization=default&date=default&host=iat.xf-yun.com");

    when(xunFeiService.sign(any())).thenReturn(Mono.just(response));

    // When & Then
    webTestClient.post()
        .uri("/xf/signature")
        .bodyValue(request)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .jsonPath("$.code").isEqualTo(200)
        .jsonPath("$.message").isEqualTo("ok")
        .jsonPath("$.data.appId").isEqualTo("default-app-id");
  }
}
