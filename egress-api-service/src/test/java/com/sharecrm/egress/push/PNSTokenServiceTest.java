package com.sharecrm.egress.push;

import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.sharecrm.egress.config.NotifyProperties;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PNSTokenServiceTest {

  @Mock
  private OkHttpSupport okHttpSupport;

  private PNSTokenService service;
  private NotifyProperties properties;

  @BeforeEach
  void setUp() {
    properties = mockConfig();
    service = new PNSTokenService(properties, okHttpSupport);
  }

  @NotNull
  private static NotifyProperties mockConfig() {
    NotifyProperties properties = new NotifyProperties();
    properties.setTokenUrl("http://test-mock");
    return properties;
  }

  @Test
  void removeAppleTokenFromOMS() {
    assertDoesNotThrow(() -> service.removeAppleTokenFromOMS("ea", "token", true));
  }

  @Test
  void removeAndroidTokenFromOMS() {
    assertDoesNotThrow(() -> service.removeAndroidTokenFromOMS("ea", "token"));
  }

  @Test
  void shouldHandleNullEaForApple() {
    assertDoesNotThrow(() -> service.removeAppleTokenFromOMS(null, "token", true));
  }

  @Test
  void shouldHandleNullTokenForApple() {
    assertDoesNotThrow(() -> service.removeAppleTokenFromOMS("ea", null, true));
  }

  @Test
  void shouldHandleEmptyEaForApple() {
    assertDoesNotThrow(() -> service.removeAppleTokenFromOMS("", "token", true));
  }

  @Test
  void shouldHandleEmptyTokenForApple() {
    assertDoesNotThrow(() -> service.removeAppleTokenFromOMS("ea", "", true));
  }

  @Test
  void shouldHandleNullEaForAndroid() {
    assertDoesNotThrow(() -> service.removeAndroidTokenFromOMS(null, "token"));
  }

  @Test
  void shouldHandleNullTokenForAndroid() {
    assertDoesNotThrow(() -> service.removeAndroidTokenFromOMS("ea", null));
  }

  @Test
  void shouldHandleEmptyEaForAndroid() {
    assertDoesNotThrow(() -> service.removeAndroidTokenFromOMS("", "token"));
  }

  @Test
  void shouldHandleEmptyTokenForAndroid() {
    assertDoesNotThrow(() -> service.removeAndroidTokenFromOMS("ea", ""));
  }

  @Test
  void shouldHandleBothProductionAndDevelopmentAppleTokens() {
    // Test production token
    assertDoesNotThrow(() -> service.removeAppleTokenFromOMS("ea", "prod-token", false));

    // Test development token
    assertDoesNotThrow(() -> service.removeAppleTokenFromOMS("ea", "dev-token", true));
  }

  @Test
  void shouldCreateServiceWithValidConfig() {
    // Given
    NotifyProperties config = new NotifyProperties();
    config.setTokenUrl("http://valid-url");
    OkHttpSupport httpSupport = mock(OkHttpSupport.class);

    // When
    PNSTokenService newService = new PNSTokenService(config, httpSupport);

    // Then
    assertNotNull(newService);
  }

  @Test
  void shouldHandleNullTokenUrl() {
    // Given
    NotifyProperties config = new NotifyProperties();
    config.setTokenUrl(null);
    OkHttpSupport httpSupport = mock(OkHttpSupport.class);

    // When & Then - should not throw exception during construction
    assertDoesNotThrow(() -> new PNSTokenService(config, httpSupport));
  }
}