package com.sharecrm.egress.service;

import com.sharecrm.egress.config.MapProperties;
import com.sharecrm.egress.entity.IpLocation;
import com.sharecrm.egress.geo.GeoProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class GeoLite2ServiceTest {

  private GeoLite2Service service;
  private MapProperties properties;

  @BeforeEach
  void setUp() {
    properties = new MapProperties();
    service = new GeoLite2Service(properties);
  }

  @Test
  void testLookupByIp() {
    // 凑数，太慢，不要真的执行
    assertThrows(Exception.class, () -> service.lookup("*************", "en"));
  }

  @Test
  void shouldReturnProvider() {
    // When
    GeoProvider provider = service.provider();

    // Then
    assertNotNull(provider);
    assertEquals(properties.getMaxmind(), provider);
  }

  @Test
  void shouldCreateQueryMono() {
    // When
    Mono<IpLocation> result = service.query("127.0.0.1", "en");

    // Then
    assertNotNull(result);
    // Note: The actual query might fail due to database not being initialized in
    // test
    // but the Mono should be created successfully
  }

  @Test
  void shouldHandleInvalidIpAddress() {
    // When
    Mono<IpLocation> result = service.query("invalid-ip", "en");

    // Then
    StepVerifier.create(result)
        .expectError()
        .verify();
  }

  @Test
  void shouldHandleNullIpAddress() {
    // When
    Mono<IpLocation> result = service.query(null, "en");

    // Then
    StepVerifier.create(result)
        .expectError()
        .verify();
  }

  @Test
  void shouldHandleEmptyIpAddress() {
    // When
    Mono<IpLocation> result = service.query("", "en");

    // Then
    StepVerifier.create(result)
        .expectError()
        .verify();
  }

  @Test
  void shouldHandleDifferentLanguages() {
    // Test with different language codes
    assertNotNull(service.query("127.0.0.1", "zh"));
    assertNotNull(service.query("127.0.0.1", "en"));
    assertNotNull(service.query("127.0.0.1", "zh-CN"));
  }
}
