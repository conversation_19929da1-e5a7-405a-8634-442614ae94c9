package com.sharecrm.egress.service;

import com.sharecrm.egress.api.I18nApi;
import com.sharecrm.egress.entity.PhoneAreaLang;
import com.sharecrm.egress.entity.PhoneAreaLangResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = I18nLocationService.class)
class I18nLocationServiceTest {

  @MockitoBean
  private I18nApi i18nApi;
  @Autowired
  private I18nLocationService i18nLocationService;

  @Test
  void queryChinese() {
    PhoneAreaLangResponse data = new PhoneAreaLangResponse();
    PhoneAreaLang lang = new PhoneAreaLang();
    lang.setProvinceLang("河南省");
    lang.setCityLang("周口市");
    data.setResult(lang);
    when(i18nApi.getI18nLocation(any())).thenReturn(Mono.just(data));
    StepVerifier.create(i18nLocationService.translate("河南", "周口", "联通", "zh-CN"))
      .expectNextMatches(loc -> "河南省".equals(loc.getProvince()) && "周口市".equals(loc.getCity()))
      .verifyComplete();
  }

  @Test
  void queryEnglish() {
    PhoneAreaLangResponse data = new PhoneAreaLangResponse();
    when(i18nApi.getI18nLocation(any())).thenReturn(Mono.just(data));

    StepVerifier.create(i18nLocationService.translate("河南", "周口", "联通", "en-US"))
      .expectNextMatches(loc -> "China Unicom".equals(loc.getCarrier()))
      .verifyComplete();
  }

  @Test
  void shouldHandleNullResult() {
    PhoneAreaLangResponse data = new PhoneAreaLangResponse();
    data.setResult(null);
    when(i18nApi.getI18nLocation(any())).thenReturn(Mono.just(data));

    StepVerifier.create(i18nLocationService.translate("河南", "周口", "联通", "zh-CN"))
        .expectNextMatches(loc -> "河南".equals(loc.getProvince()) && "周口".equals(loc.getCity()))
        .verifyComplete();
  }

  @Test
  void shouldHandleApiError() {
    when(i18nApi.getI18nLocation(any())).thenReturn(Mono.error(new RuntimeException("API error")));

    StepVerifier.create(i18nLocationService.translate("河南", "周口", "联通", "zh-CN"))
        .expectNextMatches(loc -> "河南".equals(loc.getProvince()) && "周口".equals(loc.getCity()))
        .verifyComplete();
  }

  @Test
  void shouldHandleDifferentLanguageFormats() {
    PhoneAreaLangResponse data = new PhoneAreaLangResponse();
    PhoneAreaLang lang = new PhoneAreaLang();
    lang.setProvinceLang("河南省");
    lang.setCityLang("周口市");
    data.setResult(lang);
    when(i18nApi.getI18nLocation(any())).thenReturn(Mono.just(data));

    // Test different Chinese language formats
    StepVerifier.create(i18nLocationService.translate("河南", "周口", "联通", "zh_CN"))
        .expectNextMatches(loc -> "河南省".equals(loc.getProvince()))
        .verifyComplete();

    StepVerifier.create(i18nLocationService.translate("河南", "周口", "联通", "zhCN"))
        .expectNextMatches(loc -> "河南省".equals(loc.getProvince()))
        .verifyComplete();
  }

  @Test
  void shouldHandleDifferentCarriers() {
    PhoneAreaLangResponse data = new PhoneAreaLangResponse();
    when(i18nApi.getI18nLocation(any())).thenReturn(Mono.just(data));

    // Test different carriers
    StepVerifier.create(i18nLocationService.translate("河南", "周口", "移动", "zh-CN"))
        .expectNextMatches(loc -> "移动".equals(loc.getCarrier()))
        .verifyComplete();

    StepVerifier.create(i18nLocationService.translate("河南", "周口", "电信", "zh-CN"))
        .expectNextMatches(loc -> "电信".equals(loc.getCarrier()))
        .verifyComplete();
  }

  @Test
  void shouldHandleTraditionalChinese() {
    PhoneAreaLangResponse data = new PhoneAreaLangResponse();
    when(i18nApi.getI18nLocation(any())).thenReturn(Mono.just(data));

    StepVerifier.create(i18nLocationService.translate("河南", "周口", "联通", "zh-TW"))
        .expectNextMatches(loc -> "聯通".equals(loc.getCarrier()))
        .verifyComplete();
  }

  @Test
  void shouldHandleEmptyCarrier() {
    PhoneAreaLangResponse data = new PhoneAreaLangResponse();
    PhoneAreaLang lang = new PhoneAreaLang();
    lang.setProvinceLang("河南省");
    lang.setCityLang("周口市");
    data.setResult(lang);
    when(i18nApi.getI18nLocation(any())).thenReturn(Mono.just(data));

    StepVerifier.create(i18nLocationService.translate("河南", "周口", "", "zh-CN"))
        .expectNextMatches(loc -> "河南省".equals(loc.getProvince()) && loc.getCarrier() == null)
        .verifyComplete();
  }
}