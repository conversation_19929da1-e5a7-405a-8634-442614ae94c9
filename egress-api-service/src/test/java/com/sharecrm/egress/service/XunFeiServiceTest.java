package com.sharecrm.egress.service;

import com.sharecrm.egress.config.FluxTrace;
import com.sharecrm.egress.config.XunFeiProperties;
import com.sharecrm.egress.entity.XunFeiSignatureRequest;
import com.sharecrm.egress.entity.XunFeiSignatureResponse;
import com.sharecrm.egress.sms.SmsUtils;
import com.sharecrm.egress.utils.Counters;
import com.sharecrm.egress.utils.EgressUtils;
import com.sharecrm.egress.utils.WebUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * XunFeiService单元测试
 */
@ExtendWith(MockitoExtension.class)
class XunFeiServiceTest {

  @Mock
  private XunFeiProperties properties;

  private XunFeiService service;

  @BeforeEach
  void setUp() {
    service = new XunFeiService(properties);
  }

  @Test
  void shouldSignSuccessfully() {
    // Given
    XunFeiSignatureRequest request = new XunFeiSignatureRequest();
    request.setTenantId("12345");
    request.setProtocol("wss");
    request.setMethod("GET");
    request.setHost("iat.xf-yun.com");
    request.setPath("/v1");

    XunFeiProperties.XunFeiConfig config = createMockConfig();
    Map<String, XunFeiProperties.XunFeiConfig> configs = new HashMap<>();
    configs.put("config1", config);

    when(properties.getConfigs()).thenReturn(configs);

    // When & Then
    try (MockedStatic<WebUtils> webUtilsMock = Mockito.mockStatic(WebUtils.class);
        MockedStatic<SmsUtils> smsUtilsMock = Mockito.mockStatic(SmsUtils.class);
        MockedStatic<EgressUtils> egressUtilsMock = Mockito.mockStatic(EgressUtils.class);
        MockedStatic<Counters> countersMock = Mockito.mockStatic(Counters.class)) {

      FluxTrace trace = new FluxTrace();
      trace.setEi("12345");
      webUtilsMock.when(WebUtils::fluxTrace).thenReturn(Mono.just(trace));

      smsUtilsMock.when(() -> SmsUtils.isAllowEi(anyString(), anyInt())).thenReturn(true);
      egressUtilsMock.when(() -> EgressUtils.roundRobin(anyList(), any(AtomicInteger.class))).thenReturn(config);
      countersMock.when(() -> Counters.get(anyString())).thenReturn(new AtomicInteger(0));

      Mono<XunFeiSignatureResponse> result = service.sign(Mono.just(request));

      StepVerifier.create(result)
          .assertNext(response -> {
            assertNotNull(response);
            assertEquals("test-app-id", response.getAppId());
            assertEquals("test-res-id", response.getResId());
            assertNotNull(response.getUrl());
            assertTrue(response.getUrl().startsWith("wss://iat.xf-yun.com/v1"));
          })
          .verifyComplete();
    }
  }

  @Test
  void shouldHandleEmptyTenantId() {
    // Given
    XunFeiSignatureRequest request = new XunFeiSignatureRequest();
    // tenantId为空

    XunFeiProperties.XunFeiConfig config = createMockConfig();
    Map<String, XunFeiProperties.XunFeiConfig> configs = new HashMap<>();
    configs.put("config1", config);

    when(properties.getConfigs()).thenReturn(configs);

    // When & Then
    try (MockedStatic<WebUtils> webUtilsMock = Mockito.mockStatic(WebUtils.class);
        MockedStatic<SmsUtils> smsUtilsMock = Mockito.mockStatic(SmsUtils.class);
        MockedStatic<EgressUtils> egressUtilsMock = Mockito.mockStatic(EgressUtils.class);
        MockedStatic<Counters> countersMock = Mockito.mockStatic(Counters.class)) {

      FluxTrace trace = new FluxTrace();
      trace.setEi("default-tenant");
      webUtilsMock.when(WebUtils::fluxTrace).thenReturn(Mono.just(trace));

      smsUtilsMock.when(() -> SmsUtils.isAllowEi(anyString(), anyInt())).thenReturn(true);
      egressUtilsMock.when(() -> EgressUtils.roundRobin(anyList(), any(AtomicInteger.class))).thenReturn(config);
      countersMock.when(() -> Counters.get(anyString())).thenReturn(new AtomicInteger(0));

      Mono<XunFeiSignatureResponse> result = service.sign(Mono.just(request));

      StepVerifier.create(result)
          .assertNext(response -> {
            assertNotNull(response);
            assertEquals("test-app-id", response.getAppId());
            assertEquals("test-res-id", response.getResId());
            assertNotNull(response.getUrl());
          })
          .verifyComplete();

      // 验证tenantId被设置为trace中的ei
      assertEquals("default-tenant", request.getTenantId());
    }
  }

  @Test
  void shouldThrowExceptionWhenNoConfigAvailable() {
    // Given
    XunFeiSignatureRequest request = new XunFeiSignatureRequest();
    request.setTenantId("12345");

    when(properties.getConfigs()).thenReturn(new HashMap<>());

    // When & Then
    try (MockedStatic<WebUtils> webUtilsMock = Mockito.mockStatic(WebUtils.class)) {
      FluxTrace trace = new FluxTrace();
      trace.setEi("12345");
      webUtilsMock.when(WebUtils::fluxTrace).thenReturn(Mono.just(trace));

      Mono<XunFeiSignatureResponse> result = service.sign(Mono.just(request));

      StepVerifier.create(result)
          .expectError(NullPointerException.class)
          .verify();
    }
  }

  @Test
  void shouldGenerateSignedUrlWithDefaultValues() {
    // Given
    XunFeiSignatureRequest request = new XunFeiSignatureRequest();
    // 不设置任何值，使用配置的默认值

    XunFeiProperties.XunFeiConfig config = createMockConfig();

    // When
    String result = service.generateSignedUrl(request, config);

    // Then
    assertNotNull(result);
    assertTrue(result.startsWith("wss://iat.xf-yun.com/v1"));
    assertTrue(result.contains("authorization="));
    assertTrue(result.contains("date="));
    assertTrue(result.contains("host=iat.xf-yun.com"));
  }

  @Test
  void shouldGenerateSignedUrlWithCustomValues() {
    // Given
    XunFeiSignatureRequest request = new XunFeiSignatureRequest();
    request.setProtocol("ws");
    request.setMethod("POST");
    request.setHost("custom.xf-yun.com");
    request.setPath("/v2");

    XunFeiProperties.XunFeiConfig config = createMockConfig();

    // When
    String result = service.generateSignedUrl(request, config);

    // Then
    assertNotNull(result);
    assertTrue(result.startsWith("ws://custom.xf-yun.com/v2"));
    assertTrue(result.contains("authorization="));
    assertTrue(result.contains("date="));
    assertTrue(result.contains("host=custom.xf-yun.com"));
  }

  @Test
  void shouldHandleConfigWithDisabledStatus() {
    // Given
    XunFeiSignatureRequest request = new XunFeiSignatureRequest();
    request.setTenantId("12345");

    XunFeiProperties.XunFeiConfig disabledConfig = createMockConfig();
    disabledConfig.setEnabled(false);

    Map<String, XunFeiProperties.XunFeiConfig> configs = new HashMap<>();
    configs.put("config1", disabledConfig);

    when(properties.getConfigs()).thenReturn(configs);

    // When & Then
    try (MockedStatic<WebUtils> webUtilsMock = Mockito.mockStatic(WebUtils.class)) {
      FluxTrace trace = new FluxTrace();
      trace.setEi("12345");
      webUtilsMock.when(WebUtils::fluxTrace).thenReturn(Mono.just(trace));

      Mono<XunFeiSignatureResponse> result = service.sign(Mono.just(request));

      StepVerifier.create(result)
          .expectError(NullPointerException.class)
          .verify();
    }
  }

  @Test
  void shouldHandleUnauthorizedTenant() {
    // Given
    XunFeiSignatureRequest request = new XunFeiSignatureRequest();
    request.setTenantId("99999"); // 未授权的租户

    XunFeiProperties.XunFeiConfig config = createMockConfig();
    Map<String, XunFeiProperties.XunFeiConfig> configs = new HashMap<>();
    configs.put("config1", config);

    when(properties.getConfigs()).thenReturn(configs);

    // When & Then
    try (MockedStatic<WebUtils> webUtilsMock = Mockito.mockStatic(WebUtils.class);
        MockedStatic<SmsUtils> smsUtilsMock = Mockito.mockStatic(SmsUtils.class)) {

      FluxTrace trace = new FluxTrace();
      trace.setEi("99999");
      webUtilsMock.when(WebUtils::fluxTrace).thenReturn(Mono.just(trace));

      // 模拟租户未授权
      smsUtilsMock.when(() -> SmsUtils.isAllowEi(anyString(), anyInt())).thenReturn(false);

      Mono<XunFeiSignatureResponse> result = service.sign(Mono.just(request));

      StepVerifier.create(result)
          .expectError(NullPointerException.class)
          .verify();
    }
  }

  @Test
  void shouldHandleMultipleConfigs() {
    // Given
    XunFeiSignatureRequest request = new XunFeiSignatureRequest();
    request.setTenantId("12345");

    XunFeiProperties.XunFeiConfig config1 = createMockConfig();
    config1.setAppId("app-id-1");
    XunFeiProperties.XunFeiConfig config2 = createMockConfig();
    config2.setAppId("app-id-2");

    Map<String, XunFeiProperties.XunFeiConfig> configs = new HashMap<>();
    configs.put("config1", config1);
    configs.put("config2", config2);

    when(properties.getConfigs()).thenReturn(configs);

    // When & Then
    try (MockedStatic<WebUtils> webUtilsMock = Mockito.mockStatic(WebUtils.class);
        MockedStatic<SmsUtils> smsUtilsMock = Mockito.mockStatic(SmsUtils.class);
        MockedStatic<EgressUtils> egressUtilsMock = Mockito.mockStatic(EgressUtils.class);
        MockedStatic<Counters> countersMock = Mockito.mockStatic(Counters.class)) {

      FluxTrace trace = new FluxTrace();
      trace.setEi("12345");
      webUtilsMock.when(WebUtils::fluxTrace).thenReturn(Mono.just(trace));

      smsUtilsMock.when(() -> SmsUtils.isAllowEi(anyString(), anyInt())).thenReturn(true);
      egressUtilsMock.when(() -> EgressUtils.roundRobin(anyList(), any(AtomicInteger.class))).thenReturn(config1);
      countersMock.when(() -> Counters.get(anyString())).thenReturn(new AtomicInteger(0));

      Mono<XunFeiSignatureResponse> result = service.sign(Mono.just(request));

      StepVerifier.create(result)
          .assertNext(response -> {
            assertNotNull(response);
            assertEquals("app-id-1", response.getAppId());
          })
          .verifyComplete();
    }
  }

  @Test
  void shouldHandleNullRequest() {
    // When & Then
    try (MockedStatic<WebUtils> webUtilsMock = Mockito.mockStatic(WebUtils.class)) {
      FluxTrace trace = new FluxTrace();
      trace.setEi("default");
      webUtilsMock.when(WebUtils::fluxTrace).thenReturn(Mono.just(trace));

      Mono<XunFeiSignatureResponse> result = service.sign(Mono.just(new XunFeiSignatureRequest()));

      StepVerifier.create(result)
          .expectError()
          .verify();
    }
  }

  @Test
  void shouldGenerateValidSignature() {
    // Given
    XunFeiSignatureRequest request = new XunFeiSignatureRequest();
    request.setProtocol("wss");
    request.setMethod("GET");
    request.setHost("iat.xf-yun.com");
    request.setPath("/v1");

    XunFeiProperties.XunFeiConfig config = createMockConfig();

    // When
    String result = service.generateSignedUrl(request, config);

    // Then
    assertNotNull(result);
    // 验证URL格式
    assertTrue(result.matches("wss://iat\\.xf-yun\\.com/v1\\?authorization=.+&date=.+&host=iat\\.xf-yun\\.com"));

    // 验证包含必要的参数
    assertTrue(result.contains("authorization="));
    assertTrue(result.contains("date="));
    assertTrue(result.contains("host="));
  }

  private XunFeiProperties.XunFeiConfig createMockConfig() {
    XunFeiProperties.XunFeiConfig config = new XunFeiProperties.XunFeiConfig();
    config.setEnabled(true);
    config.setAppId("test-app-id");
    config.setResId("test-res-id");
    config.setApiKey("test-api-key");
    config.setApiSecret("test-api-secret");
    config.setProtocol("wss");
    config.setMethod("GET");
    config.setHost("iat.xf-yun.com");
    config.setPath("/v1");
    config.setAllowAccounts("white:*");
    config.setWeight(1);
    return config;
  }
}
