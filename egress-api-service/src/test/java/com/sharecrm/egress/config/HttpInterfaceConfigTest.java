package com.sharecrm.egress.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sharecrm.egress.api.GoogleTranslateApi;
import com.sharecrm.egress.api.I18nApi;
import com.sharecrm.egress.entity.PhoneAreaLangResponse;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.circuitbreaker.resilience4j.ReactiveResilience4JCircuitBreakerFactory;
import org.springframework.core.ResolvableType;
import org.springframework.core.task.TaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebExceptionHandler;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class HttpInterfaceConfigTest {

  @Mock
  private TaskExecutor taskExecutor;

  @Mock
  private ReactiveResilience4JCircuitBreakerFactory breakerFactory;

  @Mock
  private RateLimiterRegistry limiterRegistry;

  private HttpInterfaceConfig config;

  @BeforeEach
  void setUp() {
    config = new HttpInterfaceConfig(taskExecutor);
  }

  @Test
  void jackson2JsonCodecs() {
    ExchangeStrategies strategies = config.jackson2JsonCodecs(new ObjectMapper());
    boolean match = strategies.messageWriters().stream()
      .anyMatch(e -> e.canWrite(ResolvableType.forClass(PhoneAreaLangResponse.class), MediaType.APPLICATION_JSON));
    assertTrue(match);
  }

  @Test
  void shouldCreateGoogleTranslateApi() {
    // Given
    TranslateProperties translateConfig = new TranslateProperties();
    translateConfig.getGoogle().setApiKey("test-key");
    translateConfig.getGoogle().setUrl("https://translate.googleapis.com");
    translateConfig.getGoogle().setReadTimeout(Duration.ofSeconds(10));

    ExchangeStrategies strategies = ExchangeStrategies.withDefaults();

    // When
    GoogleTranslateApi api = config.buildGoogleTranslateApi(strategies, translateConfig);

    // Then
    assertNotNull(api);
  }

  @Test
  void shouldCreateI18nApi() {
    // Given
    EgressProperties egressConfig = new EgressProperties();
    egressConfig.setI18nUrl("http://localhost:8080");

    ExchangeStrategies strategies = ExchangeStrategies.withDefaults();

    when(breakerFactory.getCircuitBreakerRegistry())
        .thenReturn(mock(io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry.class));
    when(breakerFactory.getCircuitBreakerRegistry().circuitBreaker(anyString()))
        .thenReturn(mock(io.github.resilience4j.circuitbreaker.CircuitBreaker.class));
    when(limiterRegistry.rateLimiter(anyString()))
        .thenReturn(mock(io.github.resilience4j.ratelimiter.RateLimiter.class));

    // When
    I18nApi api = config.buildI18nApi(breakerFactory, limiterRegistry, strategies, egressConfig);

    // Then
    assertNotNull(api);
  }

  @Test
  void shouldCreateDefaultCustomizer() {
    // When
    var customizer = config.defaultCustomizer();

    // Then
    assertNotNull(customizer);
  }

  @Test
  void shouldHandleNotFoundException() {
    // Given
    WebExceptionHandler handler = config.exceptionHandler();
    MockServerHttpRequest request = MockServerHttpRequest.get("/test").build();
    ServerWebExchange exchange = MockServerWebExchange.from(request);
    ResponseStatusException exception = new ResponseStatusException(HttpStatus.NOT_FOUND, "Not found");

    // When
    Mono<Void> result = handler.handle(exchange, exception);

    // Then
    StepVerifier.create(result)
        .verifyComplete();
    assertEquals(HttpStatus.NOT_FOUND, exchange.getResponse().getStatusCode());
  }

  @Test
  void shouldHandleGenericException() {
    // Given
    WebExceptionHandler handler = config.exceptionHandler();
    MockServerHttpRequest request = MockServerHttpRequest.get("/test").build();
    ServerWebExchange exchange = MockServerWebExchange.from(request);
    RuntimeException exception = new RuntimeException("Generic error");

    // When
    Mono<Void> result = handler.handle(exchange, exception);

    // Then
    StepVerifier.create(result)
        .expectError(RuntimeException.class)
        .verify();
  }
}