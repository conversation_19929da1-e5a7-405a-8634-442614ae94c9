package com.sharecrm.egress.config;

import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.gexin.rp.sdk.http.IGtPush;
import com.oppo.push.server.Sender;
import com.sharecrm.egress.push.NotifyPushConsumerListener;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;

import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NotifyBeanConfigTest {

  @Mock
  private NotifyProperties notifyProperties;

  @Mock
  private NotifyPushConsumerListener listener;

  @Mock
  private NotifyProperties.OppoPushConfig oppoPushConfig;

  @Mock
  private NotifyProperties.XiaoMiPushConfig xiaomiPushConfig;

  @Mock
  private NotifyProperties.GeTuiPushConfig geTuiPushConfig;

  private NotifyBeanConfig notifyBeanConfig;

  @BeforeEach
  void setUp() {
    notifyBeanConfig = new NotifyBeanConfig(notifyProperties);

    // Setup mock configurations
    when(notifyProperties.getOppo()).thenReturn(oppoPushConfig);
    when(notifyProperties.getXiaomi()).thenReturn(xiaomiPushConfig);
    when(notifyProperties.getGetui()).thenReturn(geTuiPushConfig);

    when(oppoPushConfig.getAppKey()).thenReturn("test-app-key");
    when(oppoPushConfig.getMasterSecret()).thenReturn("test-master-secret");
    when(oppoPushConfig.getProxy()).thenReturn("");

    when(xiaomiPushConfig.getAppSecret()).thenReturn("test-app-secret");
    when(xiaomiPushConfig.getProxy()).thenReturn("");

    when(geTuiPushConfig.getAppKey()).thenReturn("test-getui-key");
    when(geTuiPushConfig.getMasterSecret()).thenReturn("test-getui-secret");
    when(geTuiPushConfig.getProxy()).thenReturn("");
  }

  @Test
  void shouldCreateApplePushMessageConsumer() {
    // When
    AutoConfMQPushConsumer consumer = notifyBeanConfig.applePushMessageConsumer(listener);

    // Then
    assertNotNull(consumer);
  }

  @Test
  void shouldCreateAndroidPushMessageConsumer() {
    // When
    AutoConfMQPushConsumer consumer = notifyBeanConfig.androidPushMessageConsumer(listener);

    // Then
    assertNotNull(consumer);
  }

  @Test
  void shouldCreateVivoHttpSupportFactoryBean() {
    // When
    HttpSupportFactoryBean bean = notifyBeanConfig.vivoHttpSupportFactoryBean("test-config");

    // Then
    assertNotNull(bean);
  }

  @Test
  void shouldCreateHuaweiHttpSupportFactoryBean() {
    // When
    HttpSupportFactoryBean bean = notifyBeanConfig.huaweiHttpSupportFactoryBean("test-config");

    // Then
    assertNotNull(bean);
  }

  @Test
  void shouldCreateHonorHttpSupportFactoryBean() {
    // When
    HttpSupportFactoryBean bean = notifyBeanConfig.honorHttpSupportFactoryBean("test-config");

    // Then
    assertNotNull(bean);
  }

  @Test
  void shouldCreateOkHttpNotifyFactoryBean() {
    // When
    HttpSupportFactoryBean bean = notifyBeanConfig.okHttpNotifySupportFactoryBean();

    // Then
    assertNotNull(bean);
  }

  @Test
  void shouldCreateHttpSupportForFCM() {
    // When
    HttpSupportFactoryBean bean = notifyBeanConfig.httpSupportForFCM("test-fcm-config");

    // Then
    assertNotNull(bean);
  }

  @Test
  void shouldCreateOppoNotifySender() {
    // When
    Supplier<Sender> supplier = notifyBeanConfig.oppoNotifySender();

    // Then
    assertNotNull(supplier);
    // Note: The actual sender creation might fail due to network issues, which is
    // expected behavior
  }

  @Test
  void shouldCreateXiaomiNotifySender() {
    // When
    Supplier<com.xiaomi.xmpush.server.Sender> supplier = notifyBeanConfig.xiaomiNotifySender();

    // Then
    assertNotNull(supplier);
    // Note: The actual sender creation might fail due to network issues, which is
    // expected behavior
  }

  @Test
  void shouldCreateGeTuiNotifySender() {
    // When
    Supplier<IGtPush> supplier = notifyBeanConfig.geTuiNotifySender();

    // Then
    assertNotNull(supplier);
    // Note: The actual sender creation might fail due to network issues, which is
    // expected behavior
  }

  @Test
  void shouldHandleRefreshScopeRefreshedEvent() {
    // Given
    RefreshScopeRefreshedEvent event = mock(RefreshScopeRefreshedEvent.class);

    // When & Then - should not throw exception
    assertDoesNotThrow(() -> notifyBeanConfig.refreshNotifyClient(event));
  }

  @Test
  void shouldCallAfterPropertiesSet() {
    // When & Then - should not throw exception
    assertDoesNotThrow(() -> notifyBeanConfig.afterPropertiesSet());
  }

  @Test
  void shouldHandleOppoSenderWithProxy() {
    // Given
    when(oppoPushConfig.getProxy()).thenReturn("http://proxy.example.com:8080");

    // When
    Supplier<Sender> supplier = notifyBeanConfig.oppoNotifySender();

    // Then
    assertNotNull(supplier);
    // The supplier should handle proxy configuration
  }

  @Test
  void shouldHandleXiaomiSenderWithProxy() {
    // Given
    when(xiaomiPushConfig.getProxy()).thenReturn("http://proxy.example.com:8080");

    // When
    Supplier<com.xiaomi.xmpush.server.Sender> supplier = notifyBeanConfig.xiaomiNotifySender();

    // Then
    assertNotNull(supplier);
    // The supplier should handle proxy configuration
  }

  @Test
  void shouldHandleGeTuiSenderWithProxy() {
    // Given
    when(geTuiPushConfig.getProxy()).thenReturn("http://proxy.example.com:8080");

    // When
    Supplier<IGtPush> supplier = notifyBeanConfig.geTuiNotifySender();

    // Then
    assertNotNull(supplier);
    // The supplier should handle proxy configuration
  }

  @Test
  void shouldReturnNullWhenOppoSenderLocked() {
    // Given - simulate a locked state by calling the supplier multiple times with
    // failures
    Supplier<Sender> supplier = notifyBeanConfig.oppoNotifySender();

    // When - first call might fail and lock, second call should return null
    supplier.get(); // This might fail and create a lock
    Sender result = supplier.get();

    // Then - result could be null if locked (this is expected behavior)
    // We just verify the supplier doesn't throw exceptions
    assertNotNull(supplier);
  }
}
