package com.sharecrm.egress.config;

import com.huaweicloud.sdk.ocr.v1.OcrClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.util.List;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OcrBeanConfigTest {

  @Mock
  private OcrProperties ocrProperties;

  @Mock
  private OcrProperties.TencentConfig tencentConfig;

  @Mock
  private OcrProperties.HuaweiConfig huaweiConfig;

  private OcrBeanConfig ocrBeanConfig;

  @BeforeEach
  void setUp() {
    ocrBeanConfig = new OcrBeanConfig(ocrProperties);
    
    when(ocrProperties.getTencent()).thenReturn(tencentConfig);
    when(ocrProperties.getHuawei()).thenReturn(huaweiConfig);
  }

  @Test
  void shouldCreateTencentOcrClientWhenEnabled() {
    // Given
    when(tencentConfig.isEnabled()).thenReturn(true);
    when(tencentConfig.getSecretId()).thenReturn("test-secret-id");
    when(tencentConfig.getSecretKey()).thenReturn("test-secret-key");
    when(tencentConfig.getRegion()).thenReturn("ap-beijing");
    when(tencentConfig.getProxy()).thenReturn("");

    // When
    Supplier<com.tencentcloudapi.ocr.v20181119.OcrClient> supplier = ocrBeanConfig.tencentOcrClient();

    // Then
    assertNotNull(supplier);
    // Note: The actual client creation might fail due to network/credential issues
    // but the supplier should be created successfully
  }

  @Test
  void shouldReturnNullTencentOcrClientWhenDisabled() {
    // Given
    when(tencentConfig.isEnabled()).thenReturn(false);

    // When
    Supplier<com.tencentcloudapi.ocr.v20181119.OcrClient> supplier = ocrBeanConfig.tencentOcrClient();

    // Then
    assertNotNull(supplier);
    assertNull(supplier.get());
  }

  @Test
  void shouldCreateTencentOcrClientWithProxy() {
    // Given
    when(tencentConfig.isEnabled()).thenReturn(true);
    when(tencentConfig.getSecretId()).thenReturn("test-secret-id");
    when(tencentConfig.getSecretKey()).thenReturn("test-secret-key");
    when(tencentConfig.getRegion()).thenReturn("ap-beijing");
    when(tencentConfig.getProxy()).thenReturn("http://proxy.example.com:8080");

    // When
    Supplier<com.tencentcloudapi.ocr.v20181119.OcrClient> supplier = ocrBeanConfig.tencentOcrClient();

    // Then
    assertNotNull(supplier);
    // The supplier should handle proxy configuration
  }

  @Test
  void shouldCreateHuaweiOcrClientWhenEnabled() {
    // Given
    when(huaweiConfig.isEnabled()).thenReturn(true);
    when(huaweiConfig.getSecretId()).thenReturn("test-access-key");
    when(huaweiConfig.getSecretKey()).thenReturn("test-secret-key");
    when(huaweiConfig.getProjectId()).thenReturn("test-project-id");
    when(huaweiConfig.getRegion()).thenReturn("cn-north-4");
    when(huaweiConfig.getEndpoints()).thenReturn(List.of("https://ocr.cn-north-4.myhuaweicloud.com"));
    when(huaweiConfig.getConnectionTimeout()).thenReturn(Duration.ofSeconds(30));
    when(huaweiConfig.getProxy()).thenReturn("");

    // When
    Supplier<OcrClient> supplier = ocrBeanConfig.huaweiOcrClient();

    // Then
    assertNotNull(supplier);
    // Note: The actual client creation might fail due to network/credential issues
    // but the supplier should be created successfully
  }

  @Test
  void shouldReturnNullHuaweiOcrClientWhenDisabled() {
    // Given
    when(huaweiConfig.isEnabled()).thenReturn(false);

    // When
    Supplier<OcrClient> supplier = ocrBeanConfig.huaweiOcrClient();

    // Then
    assertNotNull(supplier);
    assertNull(supplier.get());
  }

  @Test
  void shouldCreateHuaweiOcrClientWithProxy() {
    // Given
    when(huaweiConfig.isEnabled()).thenReturn(true);
    when(huaweiConfig.getSecretId()).thenReturn("test-access-key");
    when(huaweiConfig.getSecretKey()).thenReturn("test-secret-key");
    when(huaweiConfig.getProjectId()).thenReturn("test-project-id");
    when(huaweiConfig.getRegion()).thenReturn("cn-north-4");
    when(huaweiConfig.getEndpoints()).thenReturn(List.of("https://ocr.cn-north-4.myhuaweicloud.com"));
    when(huaweiConfig.getConnectionTimeout()).thenReturn(Duration.ofSeconds(30));
    when(huaweiConfig.getProxy()).thenReturn("http://proxy.example.com:8080");

    // When
    Supplier<OcrClient> supplier = ocrBeanConfig.huaweiOcrClient();

    // Then
    assertNotNull(supplier);
    // The supplier should handle proxy configuration
  }

  @Test
  void shouldHandleEmptyProxyForTencent() {
    // Given
    when(tencentConfig.isEnabled()).thenReturn(true);
    when(tencentConfig.getSecretId()).thenReturn("test-secret-id");
    when(tencentConfig.getSecretKey()).thenReturn("test-secret-key");
    when(tencentConfig.getRegion()).thenReturn("ap-beijing");
    when(tencentConfig.getProxy()).thenReturn(null);

    // When
    Supplier<com.tencentcloudapi.ocr.v20181119.OcrClient> supplier = ocrBeanConfig.tencentOcrClient();

    // Then
    assertNotNull(supplier);
  }

  @Test
  void shouldHandleEmptyProxyForHuawei() {
    // Given
    when(huaweiConfig.isEnabled()).thenReturn(true);
    when(huaweiConfig.getSecretId()).thenReturn("test-access-key");
    when(huaweiConfig.getSecretKey()).thenReturn("test-secret-key");
    when(huaweiConfig.getProjectId()).thenReturn("test-project-id");
    when(huaweiConfig.getRegion()).thenReturn("cn-north-4");
    when(huaweiConfig.getEndpoints()).thenReturn(List.of("https://ocr.cn-north-4.myhuaweicloud.com"));
    when(huaweiConfig.getConnectionTimeout()).thenReturn(Duration.ofSeconds(30));
    when(huaweiConfig.getProxy()).thenReturn(null);

    // When
    Supplier<OcrClient> supplier = ocrBeanConfig.huaweiOcrClient();

    // Then
    assertNotNull(supplier);
  }
}
