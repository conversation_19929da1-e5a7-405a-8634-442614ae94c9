package com.sharecrm.egress.config;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 讯飞大模型配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sharecrm.api.xunfei")
public class XunFeiProperties {

  private Map<String, XunFeiConfig> configs = new HashMap<>();

  @Data
  @ToString(exclude = {"apiSecret"})
  public static class XunFeiConfig implements Weighted {

    private String id;
    private String name;
    private boolean enabled = true;

    /**
     * 权重值，数字越大权重越大分配的流量越多，建议使用小数字不要超过10
     */
    private int weight = 1;

    private String appId;
    
    private String resId;

    private String apiKey;

    private String apiSecret;

    /**
     * 支持哪些EI使用，使用灰度组件的规则判断
     */
    private String allowAccounts = "white:*";

    /**
     * 请求协议
     */
    private String protocol = "wss";

    /**
     * 请求方法
     */
    private String method = "GET";

    private String host = "iat.xf-yun.com";

    private String path = "/v1";

  }

}
