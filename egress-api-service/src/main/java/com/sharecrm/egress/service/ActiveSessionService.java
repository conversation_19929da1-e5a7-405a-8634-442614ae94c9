package com.sharecrm.egress.service;

import com.facishare.asm.api.enums.SessionStatusV2;
import com.fxiaoke.enterpriserelation2.arg.AuthWithoutEaArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.AuthUserResult;
import com.fxiaoke.enterpriserelation2.service.AuthService;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.sharecrm.egress.config.ConditionalOnGatewayMapEnabled;
import com.sharecrm.egress.config.EgressProperties;
import com.sharecrm.egress.entity.CookieToAuthArgument;
import com.sharecrm.egress.utils.WebUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.http.HttpCookie;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@ConditionalOnGatewayMapEnabled
public class ActiveSessionService {

  private static final List<String> PARAM_APP_ID = List.of("appid", "appId");
  private static final List<String> HEADER_APP_ID = List.of("fs-out-appId", "fs-out-appid");

  private final EgressProperties properties;
  private final ActiveSessionApi activeSessionApi;
  private final AuthService authService;

  /**
   * cookie cache,存储校验结果
   */
  private AsyncLoadingCache<String, Boolean> cache;

  private AsyncLoadingCache<EnterpriseKey, Boolean> enterpriseCache;

  public ActiveSessionService(EgressProperties properties, ActiveSessionApi activeSessionApi, AuthService authService) {
    this.properties = properties;
    this.activeSessionApi = activeSessionApi;
    this.authService = authService;
    initCache();
  }

  private void initCache() {
    cache = Caffeine.newBuilder()
      .expireAfterWrite(properties.getSessionTimeout())
      .maximumSize(properties.getSessionMaximumSize())
      .buildAsync((cookie, executor) -> checkFsCookie(cookie));
    enterpriseCache = Caffeine.newBuilder()
      .expireAfterWrite(properties.getSessionTimeout())
      .maximumSize(properties.getSessionMaximumSize())
      .buildAsync((key, executor) -> checkEnterpriseCookie(key));
  }


  @EventListener
  public void handleRefreshedEvent(RefreshScopeRefreshedEvent event) {
    log.info("config refreshed, refresh gateway cache. {}", event);
    initCache();
  }

  public Mono<Boolean> checkCookie(ServerWebExchange exchange) {
    if (properties.isSkipCookieAuth()) {
      log.info("gateway filter skipped cookie auth");
      return Mono.just(true);
    }
    MultiValueMap<String, HttpCookie> cookies = exchange.getRequest().getCookies();
    // 纷享账号，如果切换账号，一个登录的浏览器里可能有两种身份，某一个身份失败，再用另一个身份校验一次
    CompletableFuture<Boolean> fsCookie = checkFsCookieWithCache(cookies);

    CompletableFuture<Boolean> compose = fsCookie.thenCompose(success -> {
      if (success) {
        //已经成功了就不要继续往下请求了
        log.info("gateway filter fs cookie check success.");
        return CompletableFuture.completedFuture(true);
      }
      // 互联账号校验
      return checkEnterpriseCookieWithCache(cookies, exchange);
    });
    return Mono.fromFuture(compose);
  }

  private CompletableFuture<Boolean> checkEnterpriseCookieWithCache(MultiValueMap<String, HttpCookie> cookies, ServerWebExchange exchange) {
    String enterpriseCookie = firstCookie(cookies, properties.getEnterpriseAuthCookies());
    if (StringUtils.isBlank(enterpriseCookie)) {
      log.info("gateway filter enterprise auth cookie is empty.");
      return CompletableFuture.completedFuture(false);
    }
    log.info("gateway filter check by enterprise ERInfo cookie");
    return enterpriseCache.get(new EnterpriseKey(enterpriseCookie, getAppId(exchange)));
  }

  private CompletableFuture<Boolean> checkEnterpriseCookie(EnterpriseKey key) {
    AuthWithoutEaArg arg = new AuthWithoutEaArg();
    arg.setErInfo(key.getCookie());
    arg.setLinkAppId(key.getAppId());
    HeaderObj header = HeaderObj.newInstance(key.getAppId(), null, null, null);
    RestResult<AuthUserResult> result = authService.authWithoutEa(header, arg);
    boolean success = result.isSuccess();
    if (success) {
      log.info("gateway filter check by enterprise ERInfo cookie:{}", result);
    } else {
      log.warn("gateway filter check by enterprise ERInfo cookie failed:{}", result);
    }
    return CompletableFuture.completedFuture(success);
  }

  @NotNull
  private CompletableFuture<Boolean> checkFsCookieWithCache(MultiValueMap<String, HttpCookie> cookies) {
    String fsCookie = firstCookie(cookies, properties.getFsAuthCookies());
    if (StringUtils.isBlank(fsCookie)) {
      log.info("gateway filter fs auth cookie is empty.");
      return CompletableFuture.completedFuture(false);
    }
    log.info("gateway filter check by FSAuthXC cookie");
    return cache.get(fsCookie);
  }

  private CompletableFuture<Boolean> checkFsCookie(String cookie) {
    CookieToAuthArgument arg = new CookieToAuthArgument();
    arg.setCookie(cookie);
    arg.setRequestMethod(HttpMethod.GET.name().toUpperCase());
    return activeSessionApi.cookieToAuthXC(arg)
      //这里有个叫弱身份认证，也是允许登录的，参考fs-cep的WeekIdentity
      .map(rs -> {
        boolean success = rs.isSucceed() || !Objects.equals(rs.getSessionStatus(), SessionStatusV2.NONE.getValue());
        if (success) {
          log.debug("active session check result:{}", rs);
        } else {
          log.warn("active session check failed:{} , arg:{}", rs, arg);
        }
        return success;
      })
      // 正常情况下只会返回空，不会失败，除非session服务挂了，他挂了宁愿放行，也不要让此处服务不可用
      .onErrorResume(e -> {
        log.error("active session manage api request failed.", e);
        return Mono.just(true);
      })
      // cookieToAuthXC 无返回值时
      .switchIfEmpty(Mono.defer(() -> Mono.just(false)))
      .toFuture();
  }

  private String getAppId(ServerWebExchange exchange) {
    ServerHttpRequest req = exchange.getRequest();
    return WebUtils.firstNotBlankValue(PARAM_APP_ID, req.getQueryParams())
      .or(() -> WebUtils.firstNotBlankValue(HEADER_APP_ID, req.getHeaders()))
      .orElse(null);
  }

  private String firstCookie(MultiValueMap<String, HttpCookie> cookies, List<String> cookieNames) {
    //数组有优先级，依次获取
    for (String name : cookieNames) {
      HttpCookie first = cookies.getFirst(name);
      if (Objects.nonNull(first)) {
        return first.getValue();
      }
    }
    log.debug("cookie is empty: {}", cookieNames);
    return null;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  private static class EnterpriseKey {
    private String cookie;
    private String appId;
  }

}
