package com.sharecrm.egress.service;

import com.sharecrm.egress.entity.SmsTemplateEntity;
import com.sharecrm.egress.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collections;
import java.util.Set;

@Slf4j
@Service
public class RedisService {

  public static final String KEY_SMS = "sms:sms:";
  public static final String KEY_TTS = "sms:tts:";
  public static final String KEY_SMS_TEMPLATE = "sms:template:";
  public static final String KEY_SMS_CHANNEL = "sms:ch:";

  /**
   * 短信限流默认过期时间
   */
  private static final Duration SMS_TIMEOUT = Duration.ofSeconds(60);

  private static final Duration SMS_CHANNEL_TIMEOUT = Duration.ofSeconds(180);

  private final StringRedisTemplate redisTemplate;

  public RedisService(StringRedisTemplate redisTemplate) {
    this.redisTemplate = redisTemplate;
  }

  public boolean smsSetIfAbsent(String phone, String key) {
    try {
      return Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key(key, phone), "1", SMS_TIMEOUT));
    } catch (Exception e) {
      return true;
    }
  }

  public Set<String> getChannel(String phone) {
    try {
      String key = key(KEY_SMS_CHANNEL, phone);
      return redisTemplate.opsForSet().members(key);
    } catch (Exception e) {
      log.warn("redis get error", e);
      return Collections.emptySet();
    }
  }

  public void saveSmsTemplate(SmsTemplateEntity detail) {
    try {
      String key = key(KEY_SMS_TEMPLATE, detail.getTemplateId());
      redisTemplate.opsForValue().set(key, JsonUtil.toJson(detail), Duration.ofDays(1));
    } catch (Exception e) {
      log.warn("redis set error", e);
    }
  }

  public SmsTemplateEntity querySmsTemplate(String templateId) {
    try {
      String key = key(KEY_SMS_TEMPLATE, templateId);
      String val = redisTemplate.opsForValue().get(key);
      return StringUtils.isBlank(val) ? null : JsonUtil.fromJson(val, SmsTemplateEntity.class);
    } catch (Exception e) {
      log.warn("redis get error", e);
      return null;
    }

  }

  public void saveChannel(String phone, String channel) {
    try {
      String key = key(KEY_SMS_CHANNEL, phone);
      redisTemplate.opsForSet().add(key, channel);
      redisTemplate.opsForSet().getOperations().expire(key, SMS_CHANNEL_TIMEOUT);
    } catch (Exception e) {
      log.warn("redis save error", e);
    }
  }

  private String key(String type, String phone) {
    return type + phone;
  }

}
