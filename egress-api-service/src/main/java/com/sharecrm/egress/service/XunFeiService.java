package com.sharecrm.egress.service;

import com.sharecrm.egress.config.XunFeiProperties;
import com.sharecrm.egress.entity.XunFeiSignatureRequest;
import com.sharecrm.egress.entity.XunFeiSignatureResponse;
import com.sharecrm.egress.sms.SmsUtils;
import com.sharecrm.egress.utils.Counters;
import com.sharecrm.egress.utils.EgressUtils;
import com.sharecrm.egress.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

/**
 * 科大讯飞签名，参考文档：<a href="https://www.xfyun.cn/doc/spark/spark_zh_iat.html">...</a>
 */
@Slf4j
@Service
public class XunFeiService {

  /**
   * 加密算，仅支持hmac-sha256
   */
  private static final String ALGORITHM = "hmac-sha256";

  /**
   * headers是参与签名的参数，请注意是固定的参数名（"host date request-line"），而非这些参数的值
   */
  private static final String HEADERS = "host date request-line";

  /**
   * 当前时间戳，RFC1123格式: Tue, 14 May 2024 08:46:48 GMT
   */
  private static final DateTimeFormatter RFC1123_FORMATTER = DateTimeFormatter.RFC_1123_DATE_TIME;

  private final XunFeiProperties properties;

  public XunFeiService(XunFeiProperties properties) {
    this.properties = properties;
    log.info("xunfei configs: {}", properties);
  }

  public Mono<XunFeiSignatureResponse> sign(Mono<XunFeiSignatureRequest> request) {
    return WebUtils.fluxTrace()
      .zipWith(request)
      .map(tpl -> {
        XunFeiSignatureRequest req = tpl.getT2();
        req.setTenantId(StringUtils.defaultIfBlank(req.getTenantId(), tpl.getT1().getEi()));
        return req;
      })
      .map(this::signatureResponse);
  }

  @NotNull
  private XunFeiSignatureResponse signatureResponse(XunFeiSignatureRequest req) {
    XunFeiProperties.XunFeiConfig config = Objects.requireNonNull(selectConfig(req), "no available config");
    XunFeiSignatureResponse response = new XunFeiSignatureResponse();
    response.setAppId(config.getAppId());
    response.setResId(config.getResId());
    response.setUrl(generateSignedUrl(req, config));
    log.debug("xunfei signature response:{}", response);
    return response;
  }

  private XunFeiProperties.XunFeiConfig selectConfig(XunFeiSignatureRequest req) {
    List<XunFeiProperties.XunFeiConfig> list = properties.getConfigs()
      .values()
      .stream()
      .filter(XunFeiProperties.XunFeiConfig::isEnabled)
      //校验EI是否有权限
      .filter(e -> SmsUtils.isAllowEi(e.getAllowAccounts(), NumberUtils.toInt(req.getTenantId(), 0)))
      .toList();
    if (list.isEmpty()) {
      log.warn("No XunFei signature config found for tenant {}", req.getTenantId());
      return null;
    }
    return EgressUtils.roundRobin(list, Counters.get("xunfei"));
  }


  /**
   * 生成讯飞语音识别的签名URL
   */
  public String generateSignedUrl(XunFeiSignatureRequest request, XunFeiProperties.XunFeiConfig config) {
    try {
      String protocol = StringUtils.defaultIfEmpty(request.getProtocol(), config.getProtocol());
      String method = StringUtils.defaultIfEmpty(request.getMethod(), config.getMethod());
      String host = StringUtils.defaultIfEmpty(request.getHost(), config.getHost());
      String path = StringUtils.defaultIfEmpty(request.getPath(), config.getPath());

      // 1. 生成RFC1123格式的时间戳
      String date = generateRFC1123Date();

      // 2. 构建signature原始字段
      String requestLine = method + " " + path + " HTTP/1.1";
      String signatureOrigin = "host: " + host + "\n" + "date: " + date + "\n" + requestLine;

      // 3. 使用hmac-sha256算法结合apiSecret对signature_origin签名
      String signatureSha = hmacSha256(signatureOrigin, config.getApiSecret());

      // 4. 使用base64编码对signature_sha进行编码获得最终的signature
      String signature = base64(signatureSha);

      // 5. 拼接authorization base64编码前的字符串
      String authorizationOrigin = String.format(
        "api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"",
        config.getApiKey(), ALGORITHM, HEADERS, signature
      );

      // 6. 对authorization_origin进行base64编码获得最终的authorization参数
      String authorization = base64(authorizationOrigin);

      // 7. 构建最终的URL
      String encodedDate = URLEncoder.encode(date, StandardCharsets.UTF_8);
      String encodedAuthorization = URLEncoder.encode(authorization, StandardCharsets.UTF_8);

      return String.format("%s://%s%s?authorization=%s&date=%s&host=%s",
        protocol, host, path, encodedAuthorization, encodedDate, host);
    } catch (Exception e) {
      log.error("Generate xunfei signed url failed", e);
      return null;
    }
  }

  private String base64(String str) {
    return Base64.getEncoder().encodeToString(str.getBytes(StandardCharsets.UTF_8));
  }

  /**
   * 生成RFC1123格式的时间戳
   * 格式：Tue, 14 May 2024 08:46:48 GMT
   */
  private String generateRFC1123Date() {
    ZonedDateTime utcTime = ZonedDateTime.now(ZoneId.of("UTC"));
    return utcTime.format(RFC1123_FORMATTER);
  }

  /**
   * HMAC-SHA256加密
   */
  private String hmacSha256(String data, String key) throws Exception {
    Mac mac = Mac.getInstance("HmacSHA256");
    SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
    mac.init(secretKeySpec);
    byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
    return Base64.getEncoder().encodeToString(hash);
  }

}
