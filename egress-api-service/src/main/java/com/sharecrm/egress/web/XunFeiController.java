package com.sharecrm.egress.web;

import com.sharecrm.egress.entity.XunFeiSignatureRequest;
import com.sharecrm.egress.entity.XunFeiSignatureResponse;
import com.sharecrm.egress.sdk.entity.EgressApiResponse;
import com.sharecrm.egress.service.XunFeiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 讯飞语音识别签名URL接口
 */
@Slf4j
@RestController
@RequestMapping("/xf")
public class XunFeiController {

  private final XunFeiService service;

  public XunFeiController(XunFeiService service) {
    this.service = service;
  }

  @PostMapping("/signature")
  public Mono<ResponseEntity<EgressApiResponse<XunFeiSignatureResponse>>> signature(@RequestBody Mono<XunFeiSignatureRequest> request) {
    return service.sign(request)
      .map(e -> ResponseEntity.ok(EgressApiResponse.ok(e)))
      .onErrorReturn(ResponseEntity.ok(EgressApiResponse.badRequest()));
  }
}
