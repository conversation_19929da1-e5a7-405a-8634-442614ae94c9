package com.sharecrm.egress.entity;

import lombok.Data;

/**
 * 科大讯飞签名
 */
@Data
public class XunFeiSignatureRequest {

  /**
   * 企业ID，EI，如果不传从Header Trace中获取
   */
  private String tenantId;

  /**
   * 请求协议，可以不填，不填使用系统配置的默认值 wss
   */
  private String protocol;

  /**
   * 请求方法，可以不填，不填使用系统配置的默认值 GET
   */
  private String method;

  /**
   * 请求host，可以不填，不填使用系统配置的默认值
   */
  private String host;

  /**
   * 请求 API path，必须以/开头，可以不填，不填使用系统配置的默认值 /v1
   */
  private String path;

}
