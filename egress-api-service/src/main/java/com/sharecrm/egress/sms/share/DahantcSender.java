package com.sharecrm.egress.sms.share;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.sharecrm.egress.config.ConditionalOnSmsEnabled;
import com.sharecrm.egress.config.SmsProperties;
import com.sharecrm.egress.config.SmsProperties.DahantcConfig;
import com.sharecrm.egress.dao.SmsDao;
import com.sharecrm.egress.entity.SmsMongoEntity;
import com.sharecrm.egress.entity.SmsProvider;
import com.sharecrm.egress.entity.SmsRequestWrapper;
import com.sharecrm.egress.entity.SmsSendResult;
import com.sharecrm.egress.exception.SmsException;
import com.sharecrm.egress.sdk.entity.SmsSendRequest;
import com.sharecrm.egress.sms.AliyunUtils;
import com.sharecrm.egress.sms.SmsBeanIgnoreDestroy;
import com.sharecrm.egress.sms.SmsSender;
import com.sharecrm.egress.sms.SmsUtils;
import com.sharecrm.egress.utils.WebUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 大汉三通短信通道
 */
@Slf4j
@Service
@RefreshScope
@ConditionalOnSmsEnabled
public class DahantcSender implements SmsSender, SmsBeanIgnoreDestroy {
  private final DahantcConfig config;
  private final OkHttpSupport httpClient;
  private final SmsDao smsDao;
  private final ObjectProvider<AutoConfMQProducer> smsProducer;

  public DahantcSender(SmsProperties properties, @Qualifier("smsHttpSupport") OkHttpSupport httpClient,
                       SmsDao smsDao, @Qualifier("smsRocketMQProducer") ObjectProvider<AutoConfMQProducer> smsProducer) {
    this.config = properties.getDahantc();
    this.httpClient = httpClient;
    this.smsDao = smsDao;
    this.smsProducer = smsProducer;
    log.info("dahantc sms config: {}", config);
  }

  @Override
  public SmsSendResult send(String phone, SmsRequestWrapper<SmsSendRequest> wrapper) {
    SmsSendRequest request = SmsUtils.maskMobile(wrapper.getRequest(), config.isMaskMobileEnabled());
    try {
      Request req = requestBody(phone, request);
      Object rs = httpClient.syncExecute(req, new SyncCallback() {
        @Override
        public DahanSmsResult response(Response response) {
          return getSmsResult(response);
        }
      });
      DahanSmsResult resp = (DahanSmsResult) rs;
      SmsSendResult result = adapterResult(phone, wrapper, resp);
      result.setSign(getSign(request));
      return result;
    } catch (Exception e) {
      log.warn("send sms failed, phone :{}", phone, e);
      return SmsUtils.failed(config, wrapper, phone, e.getMessage());
    }
  }

  @NotNull
  private SmsSendResult adapterResult(String phone, SmsRequestWrapper<SmsSendRequest> wrapper, DahanSmsResult resp) {
    if (isSuccess(resp.getResult())) {
      return SmsUtils.success(config, wrapper, phone, resp.getMsgid());
    } else {
      return SmsUtils.failed(config, wrapper, phone, resp.getDesc());
    }
  }

  private boolean isSuccess(String result) {
    return "0".equals(result);
  }

  @NotNull
  Request requestBody(String phone, SmsSendRequest request) {
    DahanSmsBody smsBody = buildBody(phone, request);
    RequestBody body = RequestBody.create(JSON.toJSONString(smsBody), MediaType.parse("application/json; charset=utf-8"));
    return new Request.Builder().url(config.getUrl()).post(body).build();
  }

  DahanSmsBody buildBody(String phone, SmsSendRequest request) {
    DahanSmsBody body = basicAuthBody();
    body.setSign(getSign(request));
    body.setPhones(phone);
    body.setContent(request.getContent());
    body.setMsgid(NanoIdUtils.randomNanoId());
    return body;
  }

  private String getSign(SmsSendRequest request) {
    //大汉三通的英文签名是中文方括号
    return SmsUtils.chooseSign(request.getLanguage(), request.getContent(), config.getZhSignName(), config.getEnSignName());
  }

  DahanSmsResult getSmsResult(Response response) {
    String body = null;
    try {
      body = Objects.requireNonNull(response.body()).string();
      log.debug("dahantc response body{}", body);
      return JSON.parseObject(body, DahanSmsResult.class);
    } catch (Exception e) {
      log.warn("send sms failed. response code: {}, message: {}, body: {}", response.code(),
        response.message(), body, e);
    }
    throw new SmsException("dahantc response failed.");
  }

  public void updateStatus() {
    try {
      if (!config.isEnabled() || !config.isStatusTaskEnabled()) {
        log.debug("dahantc status task is disabled.");
        return;
      }
      Request req = WebUtils.okHttpJsonPost(config.getReportUrl(), basicAuthBody(), null);
      DahanSmsReport rs = httpClient.parseObject(req, new TypeReference<>() {
      });
      Set<DahanSmsReportData> result = rs.getReports();
      if (CollectionUtils.isEmpty(result)) {
        log.info("dahantc report is empty: {}", rs);
        return;
      }
      result.forEach(this::updateStatus);
    } catch (RuntimeException e) {
      log.warn("dahantc sms update failed", e);
    }
  }

  private DahanSmsBody basicAuthBody() {
    DahanSmsBody body = new DahanSmsBody();
    body.setAccount(config.getUsername());
    body.setPassword(SmsUtils.getMd5(config.getPassword()).toLowerCase());
    return body;
  }

  private void updateStatus(DahanSmsReportData data) {
    SmsMongoEntity entity = smsDao.updateSingleSmsStatus(config.getId(),
      data.getMsgid(), data.getPhone(), isSuccess(data.getStatus()),
      AliyunUtils.safeStrToDate(data.getSendTime()), data.getWgcode(), data.getDesc());
    if (Objects.isNull(entity)) {
      log.info("dahantc sms update no mongo entity: {}", data);
      return;
    }
    SmsUtils.sendSmsStatusEvent(entity, smsProducer.getIfAvailable());
  }

  @Override
  public SmsProvider provider() {
    return config;
  }

  @Override
  public int getOrder() {
    return config.getOrder();
  }

  @Override
  public int getWeight() {
    return config.getWeight();
  }

  @Override
  public List<String> supports() {
    return List.of(SUPPORT_CHINESE);
  }

}
